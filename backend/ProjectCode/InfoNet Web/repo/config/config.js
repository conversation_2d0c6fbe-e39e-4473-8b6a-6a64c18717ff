// config.js

const browserConfig = {
  useRemote: false,
  browserName: 'chrome',
  headless: false,
  maxConnections: 5,
};

const environmentConfig = {
  // dev: {
  //   url: 'http://localhost:3000',
  // },
  // prod: {
  //   url: 'https://example.com'
  // },
  staging: {
    url: 'https://staging.example.com',
  },
};

const timeoutConfig = {
  autoWaitForNavigation: true,
  waitUntil: ['networkidle2'],
  waitTimeout: 10000,
  screenshotOnTimeout: false,
};

const urlConfig = {
  loginPage: '/login',
  homepage: '/',
  checkoutPage: '/checkout',
  failurePage: '/failure',
};

const testDataPath = 'test_data';

module.exports = {
  browserConfig,
  environmentConfig,
  timeoutConfig,
  urlConfig,
  testDataPath,
};