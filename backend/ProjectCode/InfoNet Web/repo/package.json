{"name": "playwright-automation", "version": "1.0.0", "private": true, "scripts": {"start": "node index.js", "test:unit": "jest", "test:e2e": "playwright test e2e/**/*.js", "report:e2e": "playwright test e2e/**/*.js --reporter=spec --color=false"}, "dependencies": {"@types/jest": "^27.0.4", "@types/mocha": "^10.17.13", "@types/node": "^18.5.0", "@types/playwright": "^1.19.0", "jest": "^27.6.3"}, "devDependencies": {"@types/jest": "^27.0.4", "@types/mocha": "^10.17.13", "@types/node": "^18.5.0", "@types/playwright": "^1.19.0"}, "keywords": [], "author": "", "license": "", "main": "index.js"}