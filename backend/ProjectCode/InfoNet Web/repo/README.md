# InfoNet Web Automation Project
=====================================

## Table of Contents
-----------------

* [Project Description](#project-description)
* [Prerequisites and Setup Instructions](#prerequisites-and-setup-instructions)
* [How to Run Tests](#how-to-run-tests)
* [Project Structure Explanation](#project-structure-explanation)
* [Configuration Details](#configuration-details)
* [Reporting Information](#reporting-information)
* [Troubleshooting Tips](#troubleshooting-tips)

## Project Description
--------------------

InfoNet Web is a playwright automation project that aims to automate interactions with the InfoNet web application. The project utilizes JavaScript and the Playwright framework to provide a robust and efficient way to interact with web applications.

## Prerequisites and Setup Instructions
--------------------------------------

### Dependencies

* Node.js (>= 14.17.0)
* npm (>= 6.14.13)

### Installation

1. Clone the repository using `git clone https://github.com/your-username/info-net-web.git`
2. Install dependencies using `npm install` or `yarn install`
3. Create a new environment variable file at `.env` with your desired configuration values

## How to Run Tests
-------------------

### Test Suite

* To run all tests, use the command `npm test` or `yarn test`

### Unit Tests

* To run unit tests only, use the command `npm test:unit` or `yarn test:unit`

### Integration Tests

* To run integration tests only, use the command `npm test:integration` or `yarn test:integration`

## Project Structure Explanation
-------------------------------

The project is structured as follows:

* `src`: Contains all source code for the automation project
	+ `pages`: Holds page objects and related functionality
	+ `steps`: Holds step definitions for automated interactions
	+ `utils`: Holds utility functions used throughout the project
* `.env`: Holds environment variables used throughout the project

## Configuration Details
----------------------

The following environment variables are required:

* `INFONET_URL`: The URL of the InfoNet web application
* `USERNAME` and `PASSWORD`: Your username and password for authentication
* `TEST_MODE`: Enables or disables test mode (default: `false`)

Example `.env` file:
```markdown
INFONET_URL="https://your-info-net-url.com"
USERNAME="your-username"
PASSWORD="your-password"
TEST_MODE=true
```

## Reporting Information
----------------------

Test results will be reported in the following formats:

* HTML report at `./report/html`
* JSON report at `./report/json`

## Troubleshooting Tips
------------------------

### Common Issues

* Make sure to update the `.env` file with your desired configuration values before running tests
* Ensure that the Playwright framework is installed and configured correctly

### Error Messages

* Refer to the [Playwright documentation](https://playwright.dev/docs) for error message explanations