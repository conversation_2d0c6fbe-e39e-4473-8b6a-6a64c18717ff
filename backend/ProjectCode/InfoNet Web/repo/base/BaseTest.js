// Import required dependencies
const { test, expect } = require('@playwright/test');
const { BrowserType } = require('playwright');
const logger = require('./logger');

class BaseTest {
  static async setup() {
    // Initialize browser and page
    this.browser = await this._initializeBrowser();
    this.page = await this.browser.newPage();
  }

  static async teardown() {
    // Close browser and wait for <PERSON><PERSON> to finish loading
    await this.browser.close();
    logger.info('DOM finished loading');
  }

  _initializeBrowser() {
    return BrowserType.LATEST;
  }

  async navigate(url) {
    await this.page.goto(url);
  }

  async clickElement(selector) {
    await this.page.click(selector);
  }

  async typeText(selector, text) {
    await this.page.type(selector, text);
  }

  async verifyTitle(title) {
    const titleText = await this.page.title();
    expect(titleText).toContain(title);
  }

  async waitForCondition(condition) {
    return new Promise((resolve) => {
      this._waitUntil(() => condition(), resolve);
    });
  }

  _waitUntil(condition, callback) {
    let intervalId;
    const timeout = (ms) => setTimeout(callback, ms);

    const waitInterval = (timeoutMs) => {
      if (intervalId) return;
      intervalId = setTimeout(() => {
        this._waitUntil(condition, () => {
          clearInterval(intervalId);
          waitInterval(timeoutMs);
        });
      }, timeoutMs);
    };

    waitInterval(0);

    return new Promise((resolve) => {
      clearTimeout(intervalId);
      resolve();
    }).then(() => {
      // Reset interval
      this._waitUntil(condition, () => {
        clearInterval(intervalId);
        waitInterval(0);
      });
    });
  }

  async takeScreenshot(onFailure = false) {
    if (onFailure) {
      await this.page.screenshot({ path: 'error-screenshot.png' });
      logger.error('Test failed. Saving screenshot to error directory');
    }
  }
}

module.exports = BaseTest;
```

```javascript
// logger.js (for logging functionality)
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.printf(({ timestamp, level, message }) => `${timestamp} - ${level}: ${message}`)
  ),
  transports: [new winston.transports.Console()],
});

module.exports = logger;