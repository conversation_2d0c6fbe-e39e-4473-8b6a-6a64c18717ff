// page-object.js

const { Builder } = require('playwright');

class BasePage {
  constructor() {
    this.browser;
    this.page;
  }

  async initBrowser() {
    this.browser = await Browser.launch();
    this.page = await this.browser.newPage();
  }

  async waitForElement(elementSelector) {
    return await this.page.waitForSelector(elementSelector, { state: 'visible' });
  }

  async waitUntilCondition(condition) {
    await this.page.waitForCondition(condition);
  }

  async click(elementSelector) {
    await this.page.click(elementSelector);
  }

  async submitForm(formSelector) {
    await this.page.fill('selectoption', formSelector);
    return await this.waitForElement('#submit-button');
  }

  async selectOption(elementSelector, optionIndex) {
    const options = await this.page.$$(elementSelector);
    options[optionIndex].click();
  }

  async enterText(inputSelector, text) {
    await this.page.type('input', text);
  }

  async verifyElementExistence(elementSelector) {
    return !!await this.waitForElement(elementSelector);
  }

  async verifyElementVisibility(elementSelector) {
    const visibility = await this.page.evaluate((elementSelector) => globalThis.document.querySelector(elementSelector).visibility);
    return visibility === 'visible';
  }

  async navigateTo(url) {
    await this.page.goto(url);
    return await this.waitForElement('#title');
  }
}

module.exports = BasePage;
```

```javascript
// base-page-utils.js

const BasePage = require('./page-object');

class BasePageUtils extends BasePage {
  async waitForElementVisibility(elementSelector) {
    await this.waitUntilCondition((element) => element.isVisible());
  }

  async waitUntilElementExistence(elementSelector) {
    return !!await this.waitForElement(elementSelector);
  }
}

module.exports = BasePageUtils;
```

```javascript
// base-page-utilities.js

const { takeScreenshot } = require('playwright/lib/common');
const BasePage = require('./page-object');

class BasePageUtilities extends BasePage {
  async captureScreenshot() {
    const screenshotPath = `screenshot-${Date.now()}.png`;
    await this.page.screenshot({ path: screenshotPath });
    return screenshotPath;
  }
}

module.exports = BasePageUtilities;
```

```javascript
// base-page-assertions.js

const { expect } = require('chai');

class BasePageAssertions extends BasePage {
  async assertElementExistence(elementSelector) {
    const elementExistence = await this.verifyElementExistence(elementSelector);
    if (!elementExistence) {
      throw new Error(`Element ${elementSelector} does not exist`);
    }
  }

  async assertElementVisibility(elementSelector) {
    const visibility = await this.verifyElementVisibility(elementSelector);
    if (!visibility) {
      throw new Error(`Element ${elementSelector} is not visible`);
    }
  }
}

module.exports = BasePageAssertions;
```

```javascript
// page-object-test.js

const { test, expect } = require('@playwright/test');
const BasePage = require('./page-object');

test('Base Page Test', async ({ page }) => {
  await page.goto('http://localhost:3000');
  const basePage = new BasePage();
  await basePage.initBrowser();

  // Navigate to URL
  await basePage.navigateTo('/url');

  // Wait for element existence and visibility
  await basePage.waitUntilElementExistence('#title');
  expect(await basePage.verifyElementVisibility('#title')).toBe(true);

  // Enter text into input field
  await basePage.enterText('input-field', 'Hello World');

  // Verify element existence and visibility
  await basePage.assertElementExistence('#result');
  expect(await basePage.verifyElementVisibility('#result')).toBe(true);
});