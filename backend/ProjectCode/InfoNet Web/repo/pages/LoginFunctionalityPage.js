const { Page } = require('playwright');
const BasePage = require('./BasePage');

class LoginPage extends BasePage {
  constructor(page) {
    super(page);
    this.page = page;
  }

  // Locator for email input field
  get emailInputField() {
    return this.page.$$('#username').eq(0);
  }

  // Locator for password input field
  get passwordInputField() {
    return this.page.$$('#password').eq(0);
  }

  // Locator for login button
  get loginButton() {
    return this.page.$$('#login').eq(0);
  }

  // Locator for error message
  get errorMessage() {
    return this.page.$$('#error-message');
  }

  // Method to input email address
  async setInputEmail(email) {
    await this.emailInputField.type({ text: email });
  }

  // Method to input password
  async setPassword(password) {
    await this.passwordInputField.type({ text: password });
  }

  // Method to click login button
  async clicLogin() {
    await this.loginButton.click();
  }

  // Validation method to check if login is successful
  async isValidLogin() {
    const errorMessage = await this.errorMessage.textContent();
    return errorMessage === '';
  }

  // Error handling method to handle failed login
  async handleError(err) {
    console.error('Error occurred during login:', err);
    throw new Error(`Failed to login: ${err}`);
  }
}

module.exports = LoginPage;