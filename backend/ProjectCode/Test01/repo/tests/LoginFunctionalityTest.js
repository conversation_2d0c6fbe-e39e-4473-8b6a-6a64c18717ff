const { test, expect } = require('@playwright/test');
const LoginPage = require('./LoginPage');

class LoginPageTest extends BaseTest {
  async test1_LoginWithValidCredentials() {
    const page = await this.login();
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'djpw.djpw');
    await page.click('button[type="submit"]');
    expect(await page.locator('h2').text()).toContain('Login Successful');
  }

  async test2_LoginWithInvalidCredentials() {
    const page = await this.login();
    await page.fill('input[name="email"]', 'invalid-email');
    await page.fill('input[name="password"]', '');
    await page.click('button[type="submit"]');
    expect(await page.locator('h2').text()).toContain('Incorrect Credentials');
  }

  async test3_ForgetPasswordValidEmailAddress() {
    const page = await this.login();
    await page.click('a[href*="forgot-password"]');
    expect(await page.locator('body')).not.toContainText('Sending verification email...');
  }

  async test4_ForgetPasswordExpiredLink() {
    const page = await this.login();
    await page.click('a[href*="forgot-password"]');
    await page.waitTimeout(10000);
    await page.reload();
    expect(await page.locator('h2').text()).toContain('Forgot Password Link Expired');
  }

  async test5_ForgetPasswordInvalidLink() {
    const page = await this.login();
    await page.click('a[href*="forgot-password"]');
    for (let i = 0; i < 5; i++) {
      await page.click('a[href*="reset-password"]');
      expect(await page.locator('h2').text()).toContain('Invalid Attempt');
    }
  }

  async test6_ForgetPasswordAccountInactive() {
    const page = await this.login();
    await page.click('a[href*="forgot-password"]');
    expect(await page.locator('body')).not.toContainText('Account Inactive');
  }

  async test7_DifferentUserRolesPermissionsLogin() {
    await this.loginAsAdmin();
    await this.loginAsModerator();
    await this.loginAsStandardUser();
  }

  async test8_EdgeCaseEmptyEmailAddress() {
    const page = await this.login();
    await page.fill('input[name="email"]', '');
    await page.click('button[type="submit"]');
    expect(await page.locator('h2').text()).toContain('Invalid Email Address');
  }

  async test9_EdgeCaseNullPassword() {
    const page = await this.login();
    await page.fill('input[name="password"]', '');
    await page.click('button[type="submit"]');
    expect(await page.locator('h2').text()).toContain('Password Cannot Be Empty');
  }

  async test10_BoundaryConditionMinimumPasswordLength() {
    const page = await this.login();
    await page.fill('input[name="password"]', 'a');
    await page.click('button[type="submit"]');
    expect(await page.locator('h2').text()).toContain('Password Must Be At Least 8 Characters Long');
  }

  async test11_BoundaryConditionMaximumPasswordLength() {
    const page = await this.login();
    await page.fill('input[name="password"]', 'aaaaaaaaaaaaaaaabbbbbbbbbbbbbbcccccccccccccddddddddddddd');
    await page.click('button[type="submit"]');
    expect(await page.locator('h2').text()).toContain('Password Cannot Exceed 128 Characters');
  }

  async test12_DifferentUserRolesPermissionsForgetPassword() {
    await this.loginAsAdmin();
    await this.loginAsModerator();
    await this.loginAsStandardUser();
  }

  async test13_EdgeCaseNonASCIICharacterEmail() {
    const page = await this.login();
    await page.fill('input[name="email"]', 'tazbinur@íslendumail.is');
    await page.click('button[type="submit"]');
    expect(await page.locator('h2').text()).toContain('Login Successful');
  }

  async login() {
    const page = new LoginPage(this.context);
    return page;
  }

  async loginAsAdmin() {
    const page = new LoginPage(this.context);
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
  }

  async loginAsModerator() {
    const page = new LoginPage(this.context);
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
  }

  async loginAsStandardUser() {
    const page = new LoginPage(this.context);
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
  }
}

module.exports = LoginPageTest;