#!/usr/bin/env python3
"""
Test script to verify LLM service is working properly

This script tests:
1. Connection to Ollama
2. Basic text generation
3. Test case generation specifically

Usage:
    python test_llm_service.py
"""

import asyncio
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from app.services.llm_service import llm_service
from app.core.config import settings

async def test_basic_connection():
    """Test basic connection to Ollama."""
    print("=" * 60)
    print("Testing LLM Service Connection")
    print("=" * 60)
    print(f"Ollama URL: {settings.OLLAMA_BASE_URL}")
    print(f"Model: {settings.OLLAMA_MODEL}")
    print()
    
    try:
        response = await llm_service._make_request(
            "Hello, please respond with 'Connection successful!'",
            "You are a helpful assistant."
        )
        
        if response:
            print("✅ LLM service connection successful!")
            print(f"Response: {response}")
            return True
        else:
            print("❌ LLM service returned empty response")
            return False
            
    except Exception as e:
        print(f"❌ LLM service connection failed: {e}")
        return False

async def test_json_generation():
    """Test JSON generation capability."""
    print("\n" + "=" * 60)
    print("Testing JSON Generation")
    print("=" * 60)
    
    system_prompt = """You are a JSON generator. Always respond with valid JSON only."""
    
    prompt = """Generate a simple JSON array with 2 test objects. Each object should have:
- title: a string
- steps: a string
- expectedResult: a string

Example format:
[
  {
    "title": "Test login functionality",
    "steps": "1. Navigate to login page\n2. Enter credentials\n3. Click login",
    "expectedResult": "User should be logged in successfully"
  }
]

Generate the JSON array now:"""
    
    try:
        response = await llm_service._make_request(prompt, system_prompt)
        
        if response:
            print("✅ JSON generation response received!")
            print(f"Response length: {len(response)}")
            print(f"Response: {response}")
            
            # Try to find JSON in response
            json_start = response.find('[')
            json_end = response.rfind(']') + 1
            
            if json_start != -1 and json_end != 0:
                json_str = response[json_start:json_end]
                print(f"\n✅ JSON found in response!")
                print(f"JSON: {json_str}")
                
                # Try to parse it
                import json
                try:
                    parsed = json.loads(json_str)
                    print(f"✅ JSON parsed successfully! Found {len(parsed)} items")
                    return True
                except json.JSONDecodeError as e:
                    print(f"❌ JSON parsing failed: {e}")
                    return False
            else:
                print("❌ No JSON array found in response")
                return False
        else:
            print("❌ Empty response from LLM")
            return False
            
    except Exception as e:
        print(f"❌ JSON generation test failed: {e}")
        return False

async def test_test_case_generation():
    """Test the actual test case generation function."""
    print("\n" + "=" * 60)
    print("Testing Test Case Generation Function")
    print("=" * 60)
    
    requirement_text = """
    User Login Feature
    
    As a user, I want to be able to log into the system using my email and password
    so that I can access my personal dashboard and account information.
    
    Acceptance Criteria:
    - User can enter email and password
    - System validates credentials
    - Successful login redirects to dashboard
    - Failed login shows error message
    """
    
    try:
        test_cases = await llm_service.generate_test_cases(
            requirement_text=requirement_text,
            context="Test application with login functionality",
            rag_context="",
            existing_test_cases=[]
        )
        
        if test_cases:
            print(f"✅ Test case generation successful! Generated {len(test_cases)} test cases")
            for i, tc in enumerate(test_cases, 1):
                print(f"\nTest Case {i}:")
                print(f"  Title: {tc.title}")
                print(f"  Steps: {tc.steps[:100]}...")
                print(f"  Expected Result: {tc.expected_result[:100]}...")
            return True
        else:
            print("❌ Test case generation returned empty list")
            return False
            
    except Exception as e:
        print(f"❌ Test case generation failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🧪 LLM Service Test Suite")
    print("This will test the LLM service functionality step by step.\n")
    
    # Test 1: Basic connection
    connection_ok = await test_basic_connection()
    if not connection_ok:
        print("\n❌ Basic connection failed. Please check:")
        print("1. Ollama is running: ollama serve")
        print("2. Model is available: ollama pull llama3.2:latest")
        print("3. Environment variables are correct")
        return
    
    # Test 2: JSON generation
    json_ok = await test_json_generation()
    if not json_ok:
        print("\n⚠️  JSON generation issues detected.")
        print("The model might not be following JSON format instructions properly.")
    
    # Test 3: Test case generation
    testcase_ok = await test_test_case_generation()
    if not testcase_ok:
        print("\n❌ Test case generation failed.")
        print("This is the specific function used by the API.")
    
    # Summary
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    print(f"Basic Connection: {'✅ PASS' if connection_ok else '❌ FAIL'}")
    print(f"JSON Generation: {'✅ PASS' if json_ok else '❌ FAIL'}")
    print(f"Test Case Generation: {'✅ PASS' if testcase_ok else '❌ FAIL'}")
    
    if connection_ok and json_ok and testcase_ok:
        print("\n🎉 All tests passed! LLM service should work properly.")
    else:
        print("\n⚠️  Some tests failed. Check the issues above.")
        
        if connection_ok and not json_ok:
            print("\nSuggestion: The model might need better prompting for JSON output.")
            print("Consider using a different model or adjusting the system prompt.")

if __name__ == "__main__":
    asyncio.run(main())
